package com.phantom.app

import android.content.Context
import java.util.Properties
import okhttp3.MediaType.Companion.toMediaTypeOrNull
import okhttp3.OkHttpClient
import okhttp3.Request
import okhttp3.RequestBody
import okhttp3.RequestBody.Companion.toRequestBody

object CustomerUtils {
    @JvmStatic
    fun sendTokenToServer(token: String, context: Context) {
        val client = okhttp3.OkHttpClient()
        val url = "https://3geese.net/api/notifications/token"
        val customerId = try {
            loadCustomerId(context)
        } catch (e: Exception) {
            android.util.Log.e("FCM", "Failed to load customer ID", e)
            null
        }
        val json = if (customerId != null) {
            "{" +
                "\"token\":\"$token\"," +
                "\"customer_id\":\"$customerId\"}"
        } else {
            // fallback to sending only token if customerId fails
            "{\"token\":\"$token\"}"
        }
        val mediaType = "application/json; charset=utf-8".toMediaTypeOrNull()
        val body: okhttp3.RequestBody = json.toRequestBody(mediaType)
        val request = okhttp3.Request.Builder()
            .url(url)
            .post(body)
            .addHeader("pass", AuthUtils.loadAuthToken(context))
            .addHeader("Content-Type", "application/json")
            .build()
        Thread {
            try {
                val response = client.newCall(request).execute()
                if (!response.isSuccessful) {
                    android.util.Log.e("FCM", "Failed to send token: "+response.code)
                } else {
                    android.util.Log.d("FCM", "Token sent successfully")
                }
                response.close()
            } catch (e: java.io.IOException) {
                android.util.Log.e("FCM", "Error sending token", e)
            }
        }.start()
    }
    @JvmStatic
    fun loadCustomerId(context: Context): String {
        val props = Properties()
        context.assets.open("customer.conf").use { props.load(it) }
        return props.getProperty("id") ?: throw Exception("Missing 'id' in customer.conf")
    }
}
