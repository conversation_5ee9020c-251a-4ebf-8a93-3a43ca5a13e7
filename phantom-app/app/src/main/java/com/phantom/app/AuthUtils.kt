package com.phantom.app

import android.content.Context
import androidx.security.crypto.EncryptedSharedPreferences
import androidx.security.crypto.MasterKeys

object AuthUtils {
    fun loadAuthToken(context: Context): String {
        val sharedPrefs = EncryptedSharedPreferences.create(
            "secure_prefs",
            MasterKeys.getOrCreate(MasterKeys.AES256_GCM_SPEC),
            context.applicationContext,
            EncryptedSharedPreferences.PrefKeyEncryptionScheme.AES256_SIV,
            EncryptedSharedPreferences.PrefValueEncryptionScheme.AES256_GCM
        )
        val tokenKey = "auth_token"
        if (!sharedPrefs.contains(tokenKey)) {
            sharedPrefs.edit().putString(tokenKey, "01QIm8aW").apply()
        }
        return sharedPrefs.getString(tokenKey, null)
            ?: throw Exception("Auth token not found")
    }
}
