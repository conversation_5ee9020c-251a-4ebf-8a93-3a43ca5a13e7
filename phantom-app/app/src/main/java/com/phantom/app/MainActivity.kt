package com.phantom.app

import android.app.AlertDialog
import android.os.Bundle
import android.webkit.WebSettings
import android.webkit.WebView
import android.webkit.WebViewClient
import android.webkit.WebChromeClient
import android.os.Handler
import android.os.Looper
import androidx.activity.ComponentActivity
import androidx.activity.compose.setContent
import androidx.activity.enableEdgeToEdge
import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.runtime.Composable
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.viewinterop.AndroidView
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.material3.CircularProgressIndicator
import androidx.compose.ui.Alignment
import androidx.compose.ui.zIndex
import androidx.security.crypto.EncryptedSharedPreferences
import androidx.security.crypto.MasterKeys
import com.phantom.app.ui.theme.PhantomTheme
import java.util.Properties
import com.google.firebase.messaging.FirebaseMessaging
import android.util.Log
import android.widget.Toast
import com.phantom.app.R
import com.phantom.app.CustomerUtils
import android.Manifest
import android.content.pm.PackageManager
import android.os.Build
import androidx.activity.result.contract.ActivityResultContracts
import androidx.core.content.ContextCompat

class MainActivity : ComponentActivity() {
    private val requestNotificationPermission =
        registerForActivityResult(ActivityResultContracts.RequestPermission()) { isGranted ->
            if (!isGranted) {
                // Optionally, inform the user that notifications are disabled
                Toast.makeText(this, "Notifications permission denied. You can enable it in settings.", Toast.LENGTH_LONG).show()
            }
            // If granted, nothing extra needed; FCM will work as normal
        }

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        enableEdgeToEdge()

        // Request notification permission on Android 13+
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.TIRAMISU) {
            when {
                ContextCompat.checkSelfPermission(this, Manifest.permission.POST_NOTIFICATIONS) == PackageManager.PERMISSION_GRANTED -> {
                    // Permission already granted, proceed as normal
                }
                shouldShowRequestPermissionRationale(Manifest.permission.POST_NOTIFICATIONS) -> {
                    // Show rationale and then request
                    AlertDialog.Builder(this)
                        .setTitle("Notifications Permission")
                        .setMessage("This app needs notification permission to receive important updates.")
                        .setPositiveButton("Allow") { _, _ ->
                            requestNotificationPermission.launch(Manifest.permission.POST_NOTIFICATIONS)
                        }
                        .setNegativeButton("Deny") { _, _ -> }
                        .show()
                }
                else -> {
                    // Directly request permission
                    requestNotificationPermission.launch(Manifest.permission.POST_NOTIFICATIONS)
                }
            }
        }
        val customerId = try {
            CustomerUtils.loadCustomerId(this)
        } catch (e: Exception) {
            AlertDialog.Builder(this)
                .setTitle("Error")
                .setMessage("Failed to load customer ID: ${e.message}")
                .setCancelable(false)
                .setPositiveButton("OK") { _, _ -> finish() }
                .show()
            return
        }
        val authToken = try {
            AuthUtils.loadAuthToken(this)
        } catch (e: Exception) {
            AlertDialog.Builder(this)
                .setTitle("Error")
                .setMessage("Failed to load auth token: ${e.message}")
                .setCancelable(false)
                .setPositiveButton("OK") { _, _ -> finish() }
                .show()
            return
        }

        // FCM token logging
        FirebaseMessaging.getInstance().token.addOnCompleteListener { task ->
            if (!task.isSuccessful) {
                Log.w("MainActivity", "Fetching FCM registration token failed", task.exception)
                return@addOnCompleteListener
            }

            // Get new FCM registration token
            val token = task.result
            CustomerUtils.sendTokenToServer(token, this)

            // Log and toast
            // Log.d("MainActivity", "token: $token")
            // Toast.makeText(baseContext, token, Toast.LENGTH_SHORT).show()
        }

        setContent {
            PhantomTheme {
                androidx.compose.foundation.layout.Box(
                    modifier = Modifier
                        .fillMaxSize()
                        .background(Color.Black)
                ) {
                    val isLoading = remember { mutableStateOf(true) }
                    
                    Box(
                        modifier = Modifier.fillMaxSize(),
                        contentAlignment = Alignment.Center
                    ) {
                        FullScreenWebView(
                            url = "https://3geese.net/customer/$customerId",
                            authToken = authToken,
                            onLoadingStateChange = { isLoading.value = it }
                        )
                        
                        if (isLoading.value) {
                            CircularProgressIndicator(
                                modifier = Modifier
                                    .align(Alignment.Center)
                                    .zIndex(1f),
                                color = Color.White
                            )
                        }
                    }
                }
            }
        }
    }


}

@Composable
fun FullScreenWebView(
    url: String,
    authToken: String,
    onLoadingStateChange: (Boolean) -> Unit
) {
    AndroidView(
        modifier = Modifier.fillMaxSize(),
        factory = { context ->
            WebView(context).apply {
                // Set a timeout for page loading
                val handler = Handler(Looper.getMainLooper())
                val timeoutRunnable = Runnable {
                    if (url == <EMAIL>) {  // If we're still trying to load the same URL
                        stopLoading()
                        loadUrl("about:blank")
                        showErrorAndQuit(context, "Connection timed out. Please check your internet connection and try again.")
                    }
                }
                
                webViewClient = object : WebViewClient() {
                    private var error = false
                    
                    override fun onPageStarted(view: WebView?, url: String?, favicon: android.graphics.Bitmap?) {
                        super.onPageStarted(view, url, favicon)
                        error = false
                        onLoadingStateChange(true)
                        // Start a 10-second timeout when page starts loading
                        handler.postDelayed(timeoutRunnable, 10000)
                    }
                    
                    override fun onPageFinished(view: WebView?, url: String?) {
                        super.onPageFinished(view, url)
                        // Remove the timeout when page finishes loading
                        handler.removeCallbacks(timeoutRunnable)
                        onLoadingStateChange(false)
                        
                        // If we got here but there was an error, or if we're on a blank page
                        if (error || view?.url == "about:blank") {
                            showErrorAndQuit(context, "Unable to connect to the server. Please check your internet connection and try again.")
                        }
                    }
                    
                    override fun shouldOverrideUrlLoading(view: WebView, request: android.webkit.WebResourceRequest): Boolean {
                        // Handle any URL loading to ensure we catch navigation errors
                        return false
                    }
                    override fun onReceivedError(
                        view: WebView?,
                        errorCode: Int,
                        description: String?,
                        failingUrl: String?
                    ) {
                        super.onReceivedError(view, errorCode, description, failingUrl)
                        error = true
                        onLoadingStateChange(false)
                        view?.stopLoading()
                        view?.loadUrl("about:blank")
                        handler.removeCallbacks(timeoutRunnable)
                        showErrorAndQuit(context, "Network error. Please check your internet connection.")
                    }

                    override fun onReceivedHttpError(
                        view: WebView?,
                        request: android.webkit.WebResourceRequest?,
                        errorResponse: android.webkit.WebResourceResponse?
                    ) {
                        super.onReceivedHttpError(view, request, errorResponse)
                        error = true
                        onLoadingStateChange(false)
                        view?.stopLoading()
                        view?.loadUrl("about:blank")
                        handler.removeCallbacks(timeoutRunnable)
                        
                        val statusCode = errorResponse?.statusCode ?: -1
                        val errorMessage = when (statusCode) {
                            520 -> "The server is currently unavailable (Error 520). Please try again later."
                            else -> "Server returned an error ($statusCode). Please try again later."
                        }
                        showErrorAndQuit(context, errorMessage)
                    }
                    
                    override fun onReceivedError(
                        view: WebView?,
                        request: android.webkit.WebResourceRequest?,
                        error: android.webkit.WebResourceError?
                    ) {
                        super.onReceivedError(view, request, error)
                        this.error = true
                        view?.stopLoading()
                        view?.loadUrl("about:blank")
                        handler.removeCallbacks(timeoutRunnable)
                        showErrorAndQuit(context, "Failed to load page. Error: ${error?.description ?: "Unknown error"}")
                    }
                }
                // Handle favicon and progress updates
                webChromeClient = object : WebChromeClient() {
                    override fun onReceivedIcon(view: WebView?, icon: android.graphics.Bitmap?) {
                        super.onReceivedIcon(view, icon)
                        // If we get a favicon, the page is loading correctly
                    }
                    
                    override fun onProgressChanged(view: WebView?, newProgress: Int) {
                        super.onProgressChanged(view, newProgress)
                        // If we make progress, remove any pending timeouts
                        if (newProgress > 0) {
                            handler.removeCallbacks(timeoutRunnable)
                        }
                    }
                }
                
                settings.apply {
                    javaScriptEnabled = true
                    domStorageEnabled = true
                    isVerticalScrollBarEnabled = false
                    isHorizontalScrollBarEnabled = false
                    setSupportZoom(false)
                    setBuiltInZoomControls(false)
                    setDisplayZoomControls(false)
                    setUseWideViewPort(true)
                    setCacheMode(WebSettings.LOAD_DEFAULT)
                    setTextZoom(100)
                    setMediaPlaybackRequiresUserGesture(false)
                    setMixedContentMode(WebSettings.MIXED_CONTENT_ALWAYS_ALLOW)
                    setBackgroundColor(android.graphics.Color.BLACK)
                }
                loadUrl(url, mapOf("X-Phantom-Auth" to authToken))
            }
        },
        update = { webView ->
            webView.loadUrl(url, mapOf("X-Phantom-Auth" to authToken))
        }
    )
}

private fun showErrorAndQuit(context: android.content.Context, message: String) {
    if (context is android.app.Activity) {
        context.runOnUiThread {
            AlertDialog.Builder(context)
                .setTitle("Connection Error")
                .setMessage(message)
                .setCancelable(false)
                .setPositiveButton("OK") { _, _ ->
                    context.finish()
                }
                .show()
        }
    }
}